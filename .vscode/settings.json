// -*- jsonc -*-
{
  "debug.javascript.autoAttachFilter": "onlyWithFlag",
  "debug.toolBarLocation": "docked",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  // enable intellisense in custom snippets (see snippets.code-snippets)
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "eslint.options": {
    "reportUnusedDisableDirectives": "error"
  },
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.trimTrailingWhitespace": true,
  "prettier.prettierPath": "./node_modules/prettier/index.cjs",
  "search.exclude": {
    "**/*.code-search": true,
    "**/bower_components": true,
    "**/node_modules": true,
    ".yarn/**": true,
    "yarn.lock": true
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "jest.autoRun": { "watch": false, "onSave": "test-file" },
  "jest.jestCommandLine": "yarn test",
  "eslint.lintTask.enable": true,
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "sonarlint.connectedMode.project": {
    "connectionId": "lichtblick-suite",
    "projectKey": "Lichtblick-Suite_lichtblick"
  },
  "[ignore]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  }
}
