{"name": "lichtblick", "version": "1.19.0", "license": "MPL-2.0", "private": true, "productName": "Lichtblick", "description": "Core components of Lichtblick", "productDescription": "Lichtblick Suite", "packageManager": "yarn@3.6.3", "repository": {"type": "git", "url": "https://github.com/lichtblick-suite/lichtblick.git"}, "author": {"name": "Lichtblick", "email": "<EMAIL>"}, "homepage": "https://github.com/lichtblick-suite", "engines": {"node": ">=20"}, "scripts": {"clean": "bash ci/clean-build.sh", "build:packages": "tsc --build --verbose packages/*/tsconfig.json packages/*/src/*/tsconfig.json", "desktop:build:dev": "webpack --mode development --progress --config desktop/webpack.config.ts", "desktop:build:prod": "webpack --mode production --progress --config desktop/webpack.config.ts", "desktop:serve": "webpack serve --mode development --progress --config desktop/webpack.config.ts", "desktop:serve:quicklook": "webpack serve --mode development --progress --config desktop/webpack.quicklook.config.ts", "desktop:start": "electron desktop/.webpack", "web:build:dev": "webpack --mode development --progress --config web/webpack.config.ts", "web:build:prod": "webpack --mode production --progress --config web/webpack.config.ts", "web:serve": "webpack serve --mode development --progress --config web/webpack.config.ts", "benchmark:serve": "webpack serve --mode development --progress --config benchmark/webpack.config.ts", "benchmark:build:prod": "webpack --mode production --progress --config benchmark/webpack.config.ts", "license-check": "ts-node ci/license-check.ts", "lint": "TIMING=1 eslint --report-unused-disable-directives --fix .", "lint:ci": "TIMING=1 eslint --report-unused-disable-directives --fix --config .eslintrc.ci.yaml .", "lint:ci:report": "yarn lint:ci -f json -o eslint-report.json --quiet", "lint:unused-exports": "ts-node ./ci/lint-unused-exports.ts", "lint:dependencies": "ts-node ./ci/lint-dependencies.ts .", "test": "jest", "test:coverage": "jest --coverage", "test:debug": "cross-env NODE_OPTIONS='--inspect-brk' jest", "test:watch": "jest --watch", "test:e2e:web": "playwright test --config=e2e/tests/web/playwright.config.ts", "test:e2e:web:debug": "playwright test --config=e2e/tests/web/playwright.config.ts --debug", "test:e2e:web:report": "playwright show-report e2e/tests/web/reports", "test:e2e:desktop": "playwright test --config=e2e/tests/desktop/playwright.config.ts", "test:e2e:desktop:debug": "playwright test --config=e2e/tests/desktop/playwright.config.ts --debug", "test:e2e:desktop:report": "playwright show-report e2e/reports/desktop", "test:e2e:desktop:ci": "CI=true playwright test --config=e2e/tests/desktop/playwright.config.ts", "package:win": "yarn package --win", "package:darwin": "yarn package --mac", "package:linux": "yarn package --linux", "package": "cross-env NODE_OPTIONS='-r ts-node/register' electron-builder build --config desktop/electronBuilderConfig.js --publish never", "package:e2e": "yarn && yarn clean && yarn desktop:build:prod && yarn package", "release:bump-nightly-version": "node -r ts-node/register ./ci/bump-nightly-version.ts", "storybook": "storybook dev --port 9009 --config-dir .storybook", "storybook:build": "storybook build --config-dir .storybook --webpack-stats-json", "version:all": "yarn workspaces foreach version"}, "workspaces": {"packages": ["desktop", "packages/*", "packages/@types/*", "web", "benchmark"]}, "resolutions": {"@types/node": "20.10.0", "react-use": "patch:react-use@17.4.0#./patches/react-use.patch", "react-dnd": "patch:react-dnd@npm:16.0.1#./patches/react-dnd.patch", "@types/react": "18.3.12", "esbuild": "0.25.0"}, "devDependencies": {"@actions/core": "1.11.1", "@actions/exec": "1.1.1", "@actions/tool-cache": "2.0.2", "@babel/core": "7.28.0", "@babel/plugin-proposal-decorators": "7.28.0", "@babel/plugin-proposal-explicit-resource-management": "7.27.4", "@babel/preset-env": "7.28.0", "@babel/preset-react": "7.27.1", "@babel/preset-typescript": "7.27.1", "@lichtblick/eslint-plugin": "1.0.4", "@lichtblick/eslint-plugin-suite": "workspace:*", "@lichtblick/tsconfig": "1.0.0", "@octokit/rest": "20.0.2", "@playwright/test": "1.52.0", "@storybook/addon-actions": "9.0.8", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-interactions": "8.6.14", "@storybook/react": "9.1.0", "@storybook/react-webpack5": "9.1.0", "@types/babel__core": "^7.20.3", "@types/babel__preset-env": "^7.10.0", "@types/case-sensitive-paths-webpack-plugin": "2.1.9", "@types/jest": "30.0.0", "@types/license-checker": "^25.0.6", "@types/node": "22.9.3", "@types/react-refresh": "^0", "@types/semver": "^7.7.0", "@typescript-eslint/eslint-plugin": "8.27.0", "@typescript-eslint/parser": "8.27.0", "@typescript-eslint/rule-tester": "8.27.0", "@typescript-eslint/utils": "8.27.0", "assert": "2.1.0", "babel-plugin-transform-import-meta": "2.2.1", "cross-env": "7.0.3", "depcheck": "1.4.7", "electron": "37.2.5", "electron-builder": "26.0.12", "eslint": "8.57", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-webpack": "0.13.8", "eslint-plugin-es": "4.1.0", "eslint-plugin-file-progress": "1.4.0", "eslint-plugin-filenames": "1.3.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "27.6.3", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "0.6.15", "eslint-plugin-tss-unused-classes": "1.0.2", "globals": "15.12.0", "jest": "30.0.5", "jest-environment-jsdom": "30.0.5", "license-checker": "25.0.1", "playwright": "1.52.0", "prettier": "3.3.2", "react-refresh": "0.14.2", "rimraf": "5.0.5", "semver": "7.7.2", "storybook": "9.1.0", "ts-loader": "9.5.1", "ts-node": "10.9.2", "ts-unused-exports": "10.0.1", "tslib": "2.8.1", "typescript": "5.3.3", "webpack": "5.101.0", "webpack-cli": "6.0.1", "webpack-dev-server": "5.2.2", "webpack-hot-middleware": "2.26.0"}, "dependencies": {"@lichtblick/hooks": "workspace:*", "@lichtblick/log": "workspace:*", "@lichtblick/omgidl-parser": "1.0.0", "@lichtblick/omgidl-serialization": "1.0.0", "@lichtblick/ros2idl-parser": "1.0.0", "@lichtblick/suite": "workspace:*", "@mui/material": "5.13.5", "react-use": "17.6.0", "rehype-raw": "7.0.0", "vm-browserify": "1.1.2"}}